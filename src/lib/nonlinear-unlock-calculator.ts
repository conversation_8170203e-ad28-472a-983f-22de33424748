import type { RouterOutputs } from "~/trpc/react";

type ProjectWithDetails = NonNullable<RouterOutputs["project"]["getById"]>;

/**
 * Calculate unlock schedule using actual batch data from public imports
 * This is more accurate than estimated linear calculations
 */
export function calculateUnlockFromBatchData(
  allocation: {
    tokenAmount: number;
    unlockSchedules: Array<{
      tgeDate: Date | null;
      name?: string | null;
    }>;
  },
  batches: Array<{
    date: Date;
    unlockPercent: number;
    isTge: boolean;
  }>,
  targetDate: Date
): number {
  if (!batches || batches.length === 0) {
    return 0;
  }

  // Sort batches by date
  const sortedBatches = batches
    .filter(batch => batch.date <= targetDate)
    .sort((a, b) => a.date.getTime() - b.date.getTime());

  // Sum up all unlock percentages for batches on or before target date
  const totalUnlockedPercent = sortedBatches.reduce(
    (sum, batch) => sum + batch.unlockPercent,
    0
  );

  // Convert percentage to decimal and apply to token amount
  return Math.min(
    (totalUnlockedPercent / 100) * allocation.tokenAmount,
    allocation.tokenAmount
  );
}

/**
 * Check if we have public batch data available for more precise calculations
 */
export function hasPublicBatchData(
  project: ProjectWithDetails,
  poolName: string
): boolean {
  // This would need to be implemented based on how we store the public batch data
  // For now, we'll assume all imported projects have this data
  return true;
}