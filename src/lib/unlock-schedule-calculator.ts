import type { RouterOutputs } from "~/trpc/react";

type ProjectWithDetails = NonNullable<RouterOutputs["project"]["getById"]>;

export interface UnlockSchedulePoint {
  date: Date;
  dateLabel: string;
  totalUnlocked: Record<string, number>;
  cumulativeUnlocked: Record<string, number>;
}

export interface CalculatedUnlockSchedule {
  timeline: UnlockSchedulePoint[];
  poolNames: string[];
  totalSupply: number;
}

/**
 * Parse ISO 8601 duration string to milliseconds
 * Supports formats like "P1Y", "P6M", "P30D", "P1Y6M"
 */
function parseDurationToMs(duration: string): number {
  if (!duration || duration === "P0D") return 0;
  
  const match = duration.match(/P(?:(\d+)Y)?(?:(\d+)M)?(?:(\d+)D)?/);
  if (!match) return 0;
  
  const years = parseInt(match[1] || "0", 10);
  const months = parseInt(match[2] || "0", 10);
  const days = parseInt(match[3] || "0", 10);
  
  // Convert to days (approximate)
  const totalDays = years * 365 + months * 30 + days;
  return totalDays * 24 * 60 * 60 * 1000; // Convert to milliseconds
}

/**
 * Calculate unlock frequency in milliseconds
 */
function getUnlockFrequencyMs(frequency: string): number {
  switch (frequency) {
    case "DAILY":
      return 24 * 60 * 60 * 1000; // 1 day
    case "WEEKLY":
      return 7 * 24 * 60 * 60 * 1000; // 7 days
    case "MONTHLY":
      return 30 * 24 * 60 * 60 * 1000; // 30 days (approximate)
    case "QUARTERLY":
      return 90 * 24 * 60 * 60 * 1000; // 90 days
    case "ANNUALLY":
      return 365 * 24 * 60 * 60 * 1000; // 365 days
    case "ONE_TIME":
    default:
      return 0; // No recurring unlocks
  }
}

/**
 * Generate timeline dates for unlock schedule
 */
function generateTimelineDates(
  tgeDate: Date,
  endDate: Date,
  intervalMs: number = 30 * 24 * 60 * 60 * 1000 // Default to monthly intervals
): Date[] {
  const dates: Date[] = [tgeDate];
  let current = new Date(tgeDate);
  
  while (current < endDate) {
    current = new Date(current.getTime() + intervalMs);
    if (current <= endDate) {
      dates.push(new Date(current));
    }
  }
  
  // Always include the end date
  if (dates[dates.length - 1]?.getTime() !== endDate.getTime()) {
    dates.push(endDate);
  }
  
  return dates;
}

/**
 * Calculate unlock amount for a specific date given an unlock schedule
 */
function calculateUnlockAtDate(
  date: Date,
  allocation: {
    tokenAmount: number;
    unlockSchedules: Array<{
      unlockStartDate: Date;
      initialUnlockDecimal: number;
      lockupCliffDuration: string;
      lockupTotalDuration: string;
      unlockFrequency: string;
      unlockDecimalPerPeriod: number | null;
    }>;
    vestingSchedules?: Array<{
      vestingType: string;
      vestingMilestones: Array<{
        vestingDate: Date;
        vestingDecimal: number | null;
        vestingTokenAmount: number | null;
        isTge: boolean;
        isCliff: boolean;
      }>;
    }>;
  }
): number {
  let totalUnlocked = 0;
  
  for (const unlockSchedule of allocation.unlockSchedules) {
    const {
      unlockStartDate,
      initialUnlockDecimal,
      lockupCliffDuration,
      lockupTotalDuration,
      unlockFrequency,
      unlockDecimalPerPeriod,
    } = unlockSchedule;
    
    // If date is before unlock start, no tokens unlocked
    if (date < unlockStartDate) continue;
    
    // Calculate initial unlock (happens at unlock start date)
    let unlockedAmount = Number(initialUnlockDecimal) * allocation.tokenAmount;
    
    // Calculate cliff end date (from unlock start date)
    const cliffMs = parseDurationToMs(lockupCliffDuration);
    const cliffEndDate = new Date(unlockStartDate.getTime() + cliffMs);
    
    // If we're still in cliff period, only initial tokens are unlocked
    if (date < cliffEndDate) {
      totalUnlocked += unlockedAmount;
      continue;
    }
    
    // Calculate total unlock duration end date
    const totalDurationMs = parseDurationToMs(lockupTotalDuration);
    const unlockEndDate = new Date(unlockStartDate.getTime() + totalDurationMs);
    
    // If unlock period is complete, all tokens are unlocked
    if (date >= unlockEndDate) {
      totalUnlocked += allocation.tokenAmount;
      continue;
    }
    
    // Check if we have nonlinear vesting milestones to use instead of linear calculation
    const hasNonlinearVesting = allocation.vestingSchedules?.some(
      vs => vs.vestingType === "NONLINEAR" && vs.vestingMilestones.length > 0
    );
    
    if (hasNonlinearVesting) {
      // Use vesting milestones for nonlinear unlock calculation
      let milestonedUnlocked = 0;
      for (const vestingSchedule of allocation.vestingSchedules || []) {
        if (vestingSchedule.vestingType === "NONLINEAR") {
          for (const milestone of vestingSchedule.vestingMilestones) {
            if (milestone.vestingDate <= date) {
              if (milestone.vestingDecimal !== null) {
                milestonedUnlocked += milestone.vestingDecimal * allocation.tokenAmount;
              } else if (milestone.vestingTokenAmount !== null) {
                milestonedUnlocked += milestone.vestingTokenAmount;
              }
            }
          }
        }
      }
      unlockedAmount = Math.min(milestonedUnlocked, allocation.tokenAmount);
    } else {
      // Calculate linear unlock progress
      if (unlockDecimalPerPeriod && unlockFrequency !== "ONE_TIME") {
        const frequencyMs = getUnlockFrequencyMs(unlockFrequency);
        if (frequencyMs > 0) {
          const periodsElapsed = Math.floor(
            (date.getTime() - cliffEndDate.getTime()) / frequencyMs
          );
          
          // Calculate the remaining tokens (after initial unlock)
          const remainingTokens = allocation.tokenAmount - (Number(initialUnlockDecimal) * allocation.tokenAmount);
          
          // Calculate total periods in the unlock duration
          const totalDurationMs = parseDurationToMs(lockupTotalDuration);
          const totalPeriods = Math.ceil(totalDurationMs / frequencyMs);
          
          // Each period unlocks a portion of the remaining tokens
          const tokensPerPeriod = remainingTokens / totalPeriods;
          const additionalUnlocked = Math.min(
            periodsElapsed * tokensPerPeriod,
            remainingTokens
          );
          
          unlockedAmount += additionalUnlocked;
        }
      }
    }
    
    totalUnlocked += unlockedAmount;
  }
  
  return Math.min(totalUnlocked, allocation.tokenAmount);
}

/**
 * Calculate token unlock schedule from project data
 */
export function calculateUnlockSchedule(
  project: ProjectWithDetails
): CalculatedUnlockSchedule {
  if (!project.allocationPools || project.allocationPools.length === 0) {
    return {
      timeline: [],
      poolNames: [],
      totalSupply: 0,
    };
  }
  
  // Extract all allocations with unlock schedules
  const allocationsWithUnlocks: Array<{
    poolName: string;
    tokenAmount: number;
    unlockSchedules: Array<{
      unlockStartDate: Date;
      initialUnlockDecimal: number;
      lockupCliffDuration: string;
      lockupTotalDuration: string;
      unlockFrequency: string;
      unlockDecimalPerPeriod: number | null;
    }>;
    vestingSchedules?: Array<{
      vestingType: string;
      vestingMilestones: Array<{
        vestingDate: Date;
        vestingDecimal: number | null;
        vestingTokenAmount: number | null;
        isTge: boolean;
        isCliff: boolean;
      }>;
    }>;
  }> = [];
  
  for (const pool of project.allocationPools) {
    for (const allocation of pool.allocations) {
      if (allocation.unlockSchedules && allocation.unlockSchedules.length > 0) {
        allocationsWithUnlocks.push({
          poolName: pool.name,
          tokenAmount: Number(allocation.tokenAmount),
          unlockSchedules: allocation.unlockSchedules.map(schedule => ({
            unlockStartDate: schedule.unlockStartDate,
            initialUnlockDecimal: Number(schedule.initialUnlockDecimal),
            lockupCliffDuration: schedule.lockupCliffDuration,
            lockupTotalDuration: schedule.lockupTotalDuration,
            unlockFrequency: schedule.unlockFrequency,
            unlockDecimalPerPeriod: schedule.unlockDecimalPerPeriod ? Number(schedule.unlockDecimalPerPeriod) : null,
          })),
          vestingSchedules: allocation.vestingSchedules?.map(vestingSchedule => ({
            vestingType: vestingSchedule.vestingType,
            vestingMilestones: vestingSchedule.vestingMilestones?.map(milestone => ({
              vestingDate: milestone.vestingDate,
              vestingDecimal: milestone.vestingDecimal ? Number(milestone.vestingDecimal) : null,
              vestingTokenAmount: milestone.vestingTokenAmount ? Number(milestone.vestingTokenAmount) : null,
              isTge: milestone.isTge,
              isCliff: milestone.isCliff,
            })) || [],
          })) || [],
        });
      }
    }
  }
  
  if (allocationsWithUnlocks.length === 0) {
    return {
      timeline: [],
      poolNames: [],
      totalSupply: Number(project.totalTokenSupply),
    };
  }
  
  // Find earliest unlock start date and latest unlock completion date
  let earliestUnlockStart: Date | null = null;
  let latestUnlockEnd: Date | null = null;
  const importantDates = new Set<number>(); // Use timestamp for deduplication
  
  // Add project TGE date as an important reference point
  if (project.tgeStartDate) {
    importantDates.add(project.tgeStartDate.getTime());
    earliestUnlockStart = project.tgeStartDate;
  }
  
  for (const allocation of allocationsWithUnlocks) {
    for (const schedule of allocation.unlockSchedules) {
      if (!earliestUnlockStart || schedule.unlockStartDate < earliestUnlockStart) {
        earliestUnlockStart = schedule.unlockStartDate;
      }
      importantDates.add(schedule.unlockStartDate.getTime());
      
      const totalDurationMs = parseDurationToMs(schedule.lockupTotalDuration);
      const unlockEndDate = new Date(schedule.unlockStartDate.getTime() + totalDurationMs);
      
      if (!latestUnlockEnd || unlockEndDate > latestUnlockEnd) {
        latestUnlockEnd = unlockEndDate;
      }
      importantDates.add(unlockEndDate.getTime());
      
      // Add cliff end date as important date
      const cliffMs = parseDurationToMs(schedule.lockupCliffDuration);
      if (cliffMs > 0) {
        const cliffEndDate = new Date(schedule.unlockStartDate.getTime() + cliffMs);
        importantDates.add(cliffEndDate.getTime());
      }
    }
    
    // Add vesting milestone dates as important dates
    for (const vestingSchedule of allocation.vestingSchedules || []) {
      for (const milestone of vestingSchedule.vestingMilestones) {
        importantDates.add(milestone.vestingDate.getTime());
        if (!latestUnlockEnd || milestone.vestingDate > latestUnlockEnd) {
          latestUnlockEnd = milestone.vestingDate;
        }
      }
    }
  }
  
  if (!earliestUnlockStart || !latestUnlockEnd) {
    return {
      timeline: [],
      poolNames: [],
      totalSupply: Number(project.totalTokenSupply),
    };
  }
  
  // Generate timeline dates including important milestone dates
  const regularDates = generateTimelineDates(earliestUnlockStart, latestUnlockEnd);
  const allImportantDates = Array.from(importantDates).map(timestamp => new Date(timestamp));
  
  // Combine regular dates with important dates and sort
  const allDates = [...regularDates, ...allImportantDates];
  const uniqueDates = Array.from(
    new Map(allDates.map(date => [date.getTime(), date])).values()
  ).sort((a, b) => a.getTime() - b.getTime());
  
  const timelineDates = uniqueDates;
  
  // Get unique pool names
  const poolNames = [...new Set(allocationsWithUnlocks.map(a => a.poolName))];
  
  // Calculate unlocks for each date
  const timeline: UnlockSchedulePoint[] = timelineDates.map(date => {
    const totalUnlocked: Record<string, number> = {};
    const cumulativeUnlocked: Record<string, number> = {};
    
    // Initialize pools
    for (const poolName of poolNames) {
      totalUnlocked[poolName] = 0;
      cumulativeUnlocked[poolName] = 0;
    }
    
    // Calculate unlocks for each allocation
    for (const allocation of allocationsWithUnlocks) {
      const unlockedAmount = calculateUnlockAtDate(date, allocation);
      cumulativeUnlocked[allocation.poolName] += unlockedAmount;
    }
    
    // Copy cumulative to total for stacked area chart
    for (const poolName of poolNames) {
      totalUnlocked[poolName] = cumulativeUnlocked[poolName];
    }
    
    // Format date label based on timeline density
    let dateLabel: string;
    const timeDiffMs = latestUnlockEnd.getTime() - earliestUnlockStart.getTime();
    const timelineLength = timelineDates.length;
    
    if (timeDiffMs > 2 * 365 * 24 * 60 * 60 * 1000 || timelineLength > 24) {
      // Long timeline: use month/year format
      dateLabel = date.toLocaleDateString('en-US', { 
        month: 'short', 
        year: 'numeric' 
      });
    } else if (timeDiffMs > 6 * 30 * 24 * 60 * 60 * 1000 || timelineLength > 12) {
      // Medium timeline: use month/day/year format
      dateLabel = date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric',
        year: 'numeric' 
      });
    } else {
      // Short timeline: use full date format
      dateLabel = date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric',
        year: 'numeric'
      });
    }
    
    return {
      date,
      dateLabel,
      totalUnlocked,
      cumulativeUnlocked,
    };
  });
  
  return {
    timeline,
    poolNames,
    totalSupply: Number(project.totalTokenSupply),
  };
}