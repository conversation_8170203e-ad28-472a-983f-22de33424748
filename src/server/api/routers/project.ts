import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "~/server/api/trpc";
import { db } from "~/server/db";

export const projectRouter = createTRPCRouter({
  getAll: protectedProcedure.query(async ({ ctx }) => {
    const projects = await db.project.findMany({
      where: {
        organizationId: ctx.orgId,
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return projects;
  }),

  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const project = await db.project.findFirst({
        where: {
          id: input.id,
          organizationId: ctx.orgId,
        },
        include: {
          allocationPools: {
            include: {
              allocations: {
                include: {
                  vestingSchedules: {
                    include: {
                      vestingMilestones: {
                        orderBy: {
                          sortOrder: "asc",
                        },
                      },
                    },
                  },
                  unlockSchedules: true,
                },
              },
            },
            orderBy: {
              poolType: "asc",
            },
          },
          allocations: {
            include: {
              category: true,
              vestingSchedules: {
                include: {
                  vestingMilestones: {
                    orderBy: {
                      sortOrder: "asc",
                    },
                  },
                },
              },
              unlockSchedules: true,
            },
          },
          vestingSchedules: {
            include: {
              vestingMilestones: {
                orderBy: {
                  sortOrder: "asc",
                },
              },
            },
          },
          unlockSchedules: true,
        },
      });

      if (!project) {
        throw new Error("Project not found");
      }

      return project;
    }),
});
